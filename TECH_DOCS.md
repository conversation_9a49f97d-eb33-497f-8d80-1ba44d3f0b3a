# 技术文档 - Prorated Rent Calculator

## 项目概述

Prorated Rent Calculator 是一个基于 Next.js 的现代化 Web 应用程序，用于计算按比例分摊的租金。该应用程序帮助租户和房东计算在月中搬入或搬出时应支付的租金金额。

## 技术栈详细信息

### 前端框架
- **Next.js 15.2.4**: React 全栈框架，提供服务端渲染、路由和构建优化
- **React 19**: 用户界面库，使用最新版本的 React
- **TypeScript 5**: 提供类型安全和更好的开发体验

### UI 组件库
- **Radix UI**: 无样式、可访问的 UI 组件库
  - `@radix-ui/react-*`: 包含 40+ 个组件（accordion, dialog, select, toast 等）
- **Lucide React 0.454.0**: 现代化的图标库
- **Tailwind CSS 3.4.17**: 实用优先的 CSS 框架
- **tailwindcss-animate**: Tailwind CSS 动画插件

### 样式和主题
- **CSS Variables**: 使用 CSS 自定义属性实现主题系统
- **Geist Font**: 现代化的字体系统（Sans 和 Mono）
- **next-themes 0.4.4**: 主题切换支持
- **class-variance-authority**: 组件变体管理
- **clsx & tailwind-merge**: 条件类名和样式合并

### 表单处理
- **React Hook Form 7.54.1**: 高性能表单库
- **@hookform/resolvers 3.9.1**: 表单验证解析器
- **Zod 3.24.1**: TypeScript 优先的模式验证

### 日期处理
- **date-fns 4.1.0**: 现代化的日期工具库
- **react-day-picker 9.8.0**: 日期选择器组件

### 其他功能组件
- **Sonner 1.7.1**: 现代化的 Toast 通知系统
- **cmdk 1.0.4**: 命令面板组件
- **embla-carousel-react 8.5.1**: 轮播图组件
- **recharts 2.15.0**: React 图表库
- **react-resizable-panels 2.1.7**: 可调整大小的面板
- **vaul 0.9.6**: 抽屉组件
- **input-otp 1.4.1**: OTP 输入组件

### 分析和监控
- **Google Analytics 4**: 网站访问统计和用户行为分析
- **Next.js Script 组件**: 优化的第三方脚本加载
- **自定义事件跟踪**: 计算器使用情况监控

### SEO 优化
- **规范标签 (Canonical Tags)**: 防止重复内容问题
- **动态 Robots Meta**: 处理带参数 URL 的索引控制
- **结构化数据**: JSON-LD 格式的 Schema.org 标记
- **Open Graph & Twitter Cards**: 社交媒体分享优化
- **Sitemap.xml**: 自动生成的网站地图
- **Robots.txt**: 搜索引擎爬虫指导

### 开发工具
- **PostCSS 8.5**: CSS 后处理器
- **Autoprefixer 10.4.20**: 自动添加 CSS 前缀
- **pnpm**: 快速、节省磁盘空间的包管理器

## 项目结构

```
prorated-rent-calculator/
├── app/                    # Next.js App Router
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局组件
│   └── page.tsx           # 主页面组件
├── components/            # 可复用组件
│   ├── ui/               # UI 组件库
│   ├── analytics.tsx     # Google Analytics 组件
│   ├── seo-handler.tsx   # SEO 处理组件
│   └── theme-provider.tsx # 主题提供者
├── hooks/                # 自定义 React Hooks
├── lib/                  # 工具函数
│   ├── utils.ts          # 通用工具函数
│   └── seo-config.ts     # SEO 配置
├── public/               # 静态资源
├── styles/               # 样式文件
├── package.json          # 项目依赖
├── tailwind.config.ts    # Tailwind 配置
├── tsconfig.json         # TypeScript 配置
├── next.config.mjs       # Next.js 配置
└── postcss.config.mjs    # PostCSS 配置
```

## 核心功能实现

### 1. 租金计算逻辑
- **搬入计算**: 基于搬入日期计算剩余天数的按比例租金
- **搬出计算**: 基于搬出日期计算实际居住天数的按比例租金
- **总费用计算**: 包含按比例租金、押金和其他搬入费用

### 2. 状态管理
使用 React Hooks 进行状态管理：
- `useState`: 管理表单输入和计算结果
- `useEffect`: 响应式计算按比例租金

### 3. 响应式设计
- 使用 Tailwind CSS 实现移动端优先的响应式设计
- 支持桌面端和移动端的最佳用户体验

### 4. 可访问性
- 使用 Radix UI 确保组件的可访问性
- 语义化 HTML 结构
- 键盘导航支持

### 5. 数据分析
- **Google Analytics 4 集成**: 跟踪页面访问和用户交互
- **事件跟踪**: 监控计算器使用情况
  - `calculate_move_in_rent`: 搬入租金计算事件
  - `calculate_move_out_rent`: 搬出租金计算事件
- **环境变量管理**: 通过 `.env.local` 管理 GA ID
- **生产环境优化**: 仅在生产环境加载 Analytics 脚本

### 6. SEO 优化实现
- **规范 URL 处理**:
  - 主域名规范标签: `https://proratedrent.club/`
  - 自动处理带参数的 URL 重复问题
- **动态 Meta 标签管理**:
  - 检测 `ref` 和 UTM 参数
  - 自动添加 `noindex, follow` 防止参数页面被索引
  - 保留业务功能（跟踪推荐来源）
- **搜索引擎优化**:
  - 结构化数据 (JSON-LD Schema.org)
  - Open Graph 和 Twitter Cards
  - 自动生成 sitemap.xml 和 robots.txt
- **参数处理策略**:
  - `?ref=*`: 推荐跟踪参数，不索引但跟踪分析
  - `?utm_*`: 营销跟踪参数，不索引但保留功能

## 部署和构建

### 开发环境
```bash
pnpm dev          # 启动开发服务器
pnpm build        # 构建生产版本
pnpm start        # 启动生产服务器
pnpm lint         # 代码检查
```

### 构建优化
- Next.js 自动代码分割
- 图片优化
- CSS 压缩和优化
- TypeScript 类型检查

## 浏览器兼容性
- 现代浏览器支持（Chrome, Firefox, Safari, Edge）
- 移动端浏览器支持
- 渐进式增强设计

## 性能特性
- 服务端渲染 (SSR)
- 静态生成 (SSG)
- 自动代码分割
- 图片懒加载
- CSS-in-JS 优化

## 安全性
- TypeScript 类型安全
- 输入验证和清理
- XSS 防护
- CSRF 保护（Next.js 内置）
