'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

export default function TestSitemap() {
  const [sitemapContent, setSitemapContent] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const testSitemap = async () => {
    setLoading(true)
    setError('')
    
    try {
      const response = await fetch('/sitemap.xml')
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const contentType = response.headers.get('content-type')
      const content = await response.text()
      
      setSitemapContent(`Content-Type: ${contentType}\n\n${content}`)
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Sitemap 测试工具</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button onClick={testSitemap} disabled={loading}>
              {loading ? '测试中...' : '测试 Sitemap'}
            </Button>
            <Button 
              variant="outline" 
              onClick={() => window.open('/sitemap.xml', '_blank')}
            >
              在新窗口打开 Sitemap
            </Button>
          </div>
          
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <h3 className="font-semibold text-red-800">错误:</h3>
              <p className="text-red-700">{error}</p>
            </div>
          )}
          
          {sitemapContent && (
            <div className="space-y-2">
              <h3 className="font-semibold">Sitemap 内容:</h3>
              <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm">
                {sitemapContent}
              </pre>
            </div>
          )}
          
          <div className="space-y-2">
            <h3 className="font-semibold">验证清单:</h3>
            <ul className="list-disc pl-6 space-y-1 text-sm">
              <li>XML 声明在第一行</li>
              <li>Content-Type 为 application/xml</li>
              <li>包含正确的命名空间</li>
              <li>URL 使用 HTTPS</li>
              <li>日期格式正确 (YYYY-MM-DD)</li>
            </ul>
          </div>
          
          <div className="space-y-2">
            <h3 className="font-semibold">Google Search Console 提交步骤:</h3>
            <ol className="list-decimal pl-6 space-y-1 text-sm">
              <li>登录 <a href="https://search.google.com/search-console" target="_blank" className="text-blue-600 hover:underline">Google Search Console</a></li>
              <li>选择您的网站资源</li>
              <li>在左侧菜单点击"站点地图"</li>
              <li>点击"添加新的站点地图"</li>
              <li>输入: <code className="bg-gray-200 px-1 rounded">sitemap.xml</code></li>
              <li>点击"提交"</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
