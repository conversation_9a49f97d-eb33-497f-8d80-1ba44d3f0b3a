# Google Analytics 设置指南

## 概述

本项目已集成 Google Analytics 4 (GA4) 用于网站访问统计和用户行为分析。

## 配置步骤

### 1. 环境变量配置

在项目根目录创建 `.env.local` 文件（如果不存在）：

```bash
# Google Analytics
NEXT_PUBLIC_GA_ID=G-ZF5JTCH3CR
```

### 2. Google Analytics 控制台设置

1. 访问 [Google Analytics](https://analytics.google.com/)
2. 创建新的 GA4 属性
3. 获取测量 ID（格式：G-XXXXXXXXXX）
4. 将测量 ID 更新到 `.env.local` 文件中

## 技术实现

### Analytics 组件

位置：`components/analytics.tsx`

特性：
- 仅在生产环境加载
- 使用 Next.js Script 组件优化加载
- 支持自定义事件跟踪
- TypeScript 类型安全

### 事件跟踪

当前跟踪的事件：

1. **搬入租金计算** (`calculate_move_in_rent`)
   - 类别：calculator
   - 标签：月份_日期 (如：January_15)
   - 值：计算结果金额

2. **搬出租金计算** (`calculate_move_out_rent`)
   - 类别：calculator
   - 标签：月份_日期 (如：December_20)
   - 值：计算结果金额

### 使用自定义事件

```typescript
import { trackEvent } from '@/components/analytics'

// 跟踪自定义事件
trackEvent('button_click', 'engagement', 'header_cta', 1)
```

## 数据隐私

- 仅收集匿名使用数据
- 不收集个人身份信息
- 符合 GDPR 和其他隐私法规要求
- 用户可以通过浏览器设置禁用跟踪

## 开发环境

在开发环境中，Google Analytics 不会加载，避免污染生产数据。

## 部署注意事项

1. 确保 `.env.local` 文件不被提交到版本控制
2. 在部署平台设置环境变量 `NEXT_PUBLIC_GA_ID`
3. 验证生产环境中 GA 正常工作

## 查看数据

1. 登录 Google Analytics 控制台
2. 选择对应的属性
3. 查看实时数据和报告
4. 在"事件"部分查看自定义事件数据

## 故障排除

### 常见问题

1. **数据不显示**
   - 检查环境变量是否正确设置
   - 确认是否在生产环境
   - 验证 GA ID 格式正确

2. **事件不跟踪**
   - 检查浏览器控制台是否有错误
   - 确认 gtag 函数已正确加载
   - 验证事件参数格式

3. **开发环境加载 GA**
   - 检查 `NODE_ENV` 环境变量
   - 确认 Analytics 组件的环境判断逻辑

### 调试方法

在浏览器控制台中检查：

```javascript
// 检查 gtag 是否已加载
console.log(typeof window.gtag)

// 检查 dataLayer
console.log(window.dataLayer)
```
