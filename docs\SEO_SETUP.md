# SEO 优化设置指南

## 概述

本项目已实现完整的 SEO 优化方案，解决 Google 索引问题，特别是处理带参数 URL 的重复内容问题。

## 核心功能

### 1. 规范标签 (Canonical Tags)
- **主规范 URL**: `https://proratedrent.club/`
- **自动处理**: 所有页面都指向主域名
- **实现位置**: `app/layout.tsx` 中的 metadata

### 2. 动态参数处理
自动检测和处理以下参数：
- `ref=*`: 推荐跟踪参数
- `utm_source`, `utm_medium`, `utm_campaign`: 营销跟踪参数
- `utm_term`, `utm_content`: 详细跟踪参数

### 3. SEO Handler 组件
位置：`components/seo-handler.tsx`

功能：
- 检测 URL 参数
- 动态添加 `noindex, follow` meta 标签
- 保留业务跟踪功能
- 记录分析事件

## 技术实现

### 规范标签设置
```typescript
// lib/seo-config.ts
export const metadata = {
  alternates: {
    canonical: 'https://proratedrent.club/',
  },
}
```

### 动态 Robots Meta 标签
```typescript
// 检测参数并添加 noindex
if (refParam || hasUtmParams) {
  const metaRobots = document.createElement('meta')
  metaRobots.name = 'robots'
  metaRobots.content = 'noindex, follow'
  document.head.appendChild(metaRobots)
}
```

### Robots.txt 配置
```
User-agent: *
Allow: /

# 禁止索引带有跟踪参数的页面
Disallow: /*?ref=*
Disallow: /*?utm_*
Disallow: /*&ref=*
Disallow: /*&utm_*

Sitemap: https://proratedrent.club/sitemap.xml
```

## 结构化数据

### Schema.org 标记
```json
{
  "@context": "https://schema.org",
  "@type": "WebApplication",
  "name": "Prorated Rent Calculator",
  "description": "Calculate prorated rent for move-in and move-out dates",
  "url": "https://proratedrent.club",
  "applicationCategory": "FinanceApplication"
}
```

### Open Graph 标签
- `og:title`: 页面标题
- `og:description`: 页面描述
- `og:url`: 规范 URL
- `og:type`: website
- `og:image`: 社交分享图片

### Twitter Cards
- `twitter:card`: summary_large_image
- `twitter:title`: 页面标题
- `twitter:description`: 页面描述
- `twitter:image`: 分享图片

## 自动生成文件

### Sitemap.xml
- 路径: `/sitemap.xml`
- 自动更新日期
- 包含主页面信息
- 设置优先级和更新频率

### Robots.txt
- 路径: `/robots.txt`
- 允许所有爬虫访问主页
- 禁止索引带参数的页面
- 指向 sitemap 位置

## 参数处理策略

### 推荐参数 (ref)
```
原始 URL: https://proratedrent.club/?ref=social
处理结果:
- 添加 noindex meta 标签
- 跟踪推荐来源到 GA
- 保留页面功能
- 规范 URL 仍指向主页
```

### UTM 参数
```
原始 URL: https://proratedrent.club/?utm_source=google&utm_medium=cpc
处理结果:
- 添加 noindex meta 标签
- 跟踪营销来源到 GA
- 保留页面功能
- 规范 URL 仍指向主页
```

## 验证方法

### 1. Google Search Console
- 提交 sitemap.xml
- 监控索引状态
- 检查规范 URL 设置

### 2. 开发者工具检查
```javascript
// 检查规范标签
document.querySelector('link[rel="canonical"]').href

// 检查 robots meta
document.querySelector('meta[name="robots"]').content

// 检查结构化数据
JSON.parse(document.querySelector('script[type="application/ld+json"]').textContent)
```

### 3. SEO 工具验证
- Google Rich Results Test
- Facebook Sharing Debugger
- Twitter Card Validator

## 最佳实践

1. **保持规范 URL 一致性**
   - 所有页面都指向主域名
   - 避免多个规范 URL

2. **参数处理原则**
   - 跟踪参数不影响 SEO
   - 保留业务功能
   - 防止重复内容

3. **监控和维护**
   - 定期检查 Search Console
   - 监控索引状态
   - 更新 sitemap

## 故障排除

### 常见问题

1. **参数页面仍被索引**
   - 检查 robots meta 标签是否正确添加
   - 验证 robots.txt 配置
   - 等待搜索引擎重新爬取

2. **规范标签不生效**
   - 确认标签格式正确
   - 检查 URL 是否可访问
   - 验证没有冲突的规范标签

3. **结构化数据错误**
   - 使用 Google Rich Results Test 验证
   - 检查 JSON-LD 格式
   - 确认必需字段完整
