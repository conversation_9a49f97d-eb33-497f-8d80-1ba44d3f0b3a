export async function GET() {
  const baseUrl = 'https://proratedrent.club'
  const currentDate = new Date().toISOString().split('T')[0]
  
  const sitemapContent = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${baseUrl}/</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>`

  // 使用 Buffer 确保正确的编码
  const buffer = Buffer.from(sitemapContent, 'utf8')

  return new Response(buffer, {
    status: 200,
    headers: {
      'Content-Type': 'application/xml; charset=utf-8',
      'Content-Length': buffer.length.toString(),
      'Cache-Control': 'public, max-age=86400',
    },
  })
}
