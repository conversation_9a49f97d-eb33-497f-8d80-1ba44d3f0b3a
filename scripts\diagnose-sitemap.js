// Sitemap 诊断脚本
const https = require('https');

function diagnoseSitemap(url) {
  console.log(`🔍 诊断 sitemap: ${url}`);
  console.log('=' .repeat(50));
  
  const urlObj = new URL(url);
  
  const options = {
    hostname: urlObj.hostname,
    port: urlObj.port || 443,
    path: urlObj.pathname,
    method: 'GET',
    headers: {
      'User-Agent': 'Mozilla/5.0 (compatible; SitemapBot/1.0)',
      'Accept': 'application/xml,text/xml,*/*'
    }
  };

  const req = https.request(options, (res) => {
    console.log(`📊 响应状态: ${res.statusCode} ${res.statusMessage}`);
    console.log('📋 响应头:');
    Object.entries(res.headers).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`);
    });
    console.log('');

    let data = '';
    let rawData = Buffer.alloc(0);

    res.on('data', (chunk) => {
      data += chunk;
      rawData = Buffer.concat([rawData, chunk]);
    });

    res.on('end', () => {
      console.log('📄 原始数据长度:', rawData.length);
      console.log('📝 文本数据长度:', data.length);
      console.log('');
      
      // 检查 BOM
      if (rawData.length >= 3 && rawData[0] === 0xEF && rawData[1] === 0xBB && rawData[2] === 0xBF) {
        console.log('⚠️  检测到 UTF-8 BOM');
      }
      
      // 显示前几个字节
      console.log('🔢 前 20 个字节 (hex):');
      console.log(rawData.slice(0, 20).toString('hex').match(/.{2}/g).join(' '));
      console.log('');
      
      console.log('📖 内容:');
      console.log('-'.repeat(40));
      console.log(data);
      console.log('-'.repeat(40));
      console.log('');
      
      // XML 验证
      console.log('✅ XML 验证:');
      const checks = [
        {
          name: 'XML 声明存在',
          test: data.includes('<?xml'),
          fix: '确保第一行是 <?xml version="1.0" encoding="UTF-8"?>'
        },
        {
          name: 'UTF-8 编码声明',
          test: data.includes('encoding="UTF-8"'),
          fix: '在 XML 声明中添加 encoding="UTF-8"'
        },
        {
          name: 'urlset 根元素',
          test: data.includes('<urlset'),
          fix: '确保有 <urlset> 根元素'
        },
        {
          name: 'sitemap 命名空间',
          test: data.includes('xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"'),
          fix: '添加正确的 sitemap 命名空间'
        },
        {
          name: 'url 元素存在',
          test: data.includes('<url>'),
          fix: '添加至少一个 <url> 元素'
        },
        {
          name: 'loc 元素存在',
          test: data.includes('<loc>'),
          fix: '每个 <url> 必须包含 <loc> 元素'
        },
        {
          name: 'HTTPS URL',
          test: data.includes('<loc>https://'),
          fix: '使用 HTTPS URL'
        }
      ];
      
      let allPassed = true;
      checks.forEach(check => {
        const status = check.test ? '✅' : '❌';
        console.log(`  ${status} ${check.name}`);
        if (!check.test) {
          console.log(`     💡 修复建议: ${check.fix}`);
          allPassed = false;
        }
      });
      
      console.log('');
      console.log(`🎯 总体结果: ${allPassed ? '✅ 通过所有检查' : '❌ 需要修复'}`);
      
      if (!allPassed) {
        console.log('');
        console.log('🔧 建议的完整 sitemap 格式:');
        console.log(`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://proratedrent.club/</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>`);
      }
    });
  });

  req.on('error', (err) => {
    console.error(`❌ 请求错误: ${err.message}`);
  });

  req.setTimeout(10000, () => {
    console.error('❌ 请求超时');
    req.destroy();
  });

  req.end();
}

// 诊断生产环境
console.log('🚀 开始诊断...\n');
diagnoseSitemap('https://proratedrent.club/sitemap.xml');
