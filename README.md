# Prorated Rent Calculator

一个现代化的按比例租金计算器，帮助租户和房东计算月中搬入或搬出时的公平租金。

## 🌟 功能特性

- **搬入计算器**: 计算月中搬入时的按比例租金
- **搬出计算器**: 计算月中搬出时应支付的租金
- **总费用计算**: 包含押金和其他搬入费用
- **响应式设计**: 完美支持桌面端和移动端
- **现代化界面**: 使用 Tailwind CSS 和 Radix UI 构建
- **实时计算**: 输入数据后立即显示计算结果

## 🚀 技术栈

- **框架**: Next.js 15.2.4 + React 19
- **语言**: TypeScript 5
- **样式**: Tailwind CSS 3.4.17
- **组件**: Radix UI + Lucide React
- **表单**: React Hook Form + Zod
- **字体**: Geist Sans & Mono
- **包管理**: pnpm

## 📦 安装和运行

### 环境要求
- Node.js 18+
- pnpm (推荐) 或 npm/yarn

### 安装依赖
```bash
pnpm install
```

### 开发环境
```bash
pnpm dev
```
访问 [http://localhost:3000](http://localhost:3000) 查看应用

### 构建生产版本
```bash
pnpm build
pnpm start
```

### 代码检查
```bash
pnpm lint
```

## 🎯 使用方法

### 搬入计算
1. 输入月租金额
2. 选择搬入月份和日期
3. 可选：添加押金和其他费用
4. 查看计算结果

### 搬出计算
1. 输入月租金额
2. 选择搬出月份和日期
3. 查看按比例租金

## 📁 项目结构

```
prorated-rent-calculator/
├── app/                    # Next.js App Router
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页面
├── components/            # 可复用组件
│   ├── ui/               # UI 组件库
│   └── theme-provider.tsx
├── hooks/                # 自定义 Hooks
├── lib/                  # 工具函数
├── public/               # 静态资源
└── styles/               # 样式文件
```

## 🔧 配置文件

- `next.config.mjs` - Next.js 配置
- `tailwind.config.ts` - Tailwind CSS 配置
- `tsconfig.json` - TypeScript 配置
- `postcss.config.mjs` - PostCSS 配置
- `components.json` - shadcn/ui 配置

## 📚 详细文档

查看 [TECH_DOCS.md](./TECH_DOCS.md) 了解详细的技术实现和架构信息。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 🔗 相关链接

- [Next.js 文档](https://nextjs.org/docs)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [Radix UI 文档](https://www.radix-ui.com/docs)