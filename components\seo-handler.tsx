'use client'

import { useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { trackEvent } from './analytics'

export function SEOHandler() {
  const searchParams = useSearchParams()

  useEffect(() => {
    // 检查是否有 ref 参数
    const refParam = searchParams.get('ref')
    
    if (refParam) {
      // 跟踪推荐来源（保留业务功能）
      trackEvent('referral_visit', 'traffic', refParam)
      
      // 添加 noindex meta 标签告知搜索引擎不要索引带参数的页面
      const existingNoIndexMeta = document.querySelector('meta[name="robots"][content*="noindex"]')
      
      if (!existingNoIndexMeta) {
        const metaRobots = document.createElement('meta')
        metaRobots.name = 'robots'
        metaRobots.content = 'noindex, follow'
        document.head.appendChild(metaRobots)
      }
      
      // 可选：在控制台记录（开发调试用）
      if (process.env.NODE_ENV === 'development') {
        console.log('SEO: Added noindex for ref parameter:', refParam)
      }
    }
    
    // 处理其他可能影响 SEO 的参数
    const utmParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content']
    const hasUtmParams = utmParams.some(param => searchParams.get(param))
    
    if (hasUtmParams && !searchParams.get('ref')) {
      // 如果只有 UTM 参数但没有 ref 参数，也添加 noindex
      const existingNoIndexMeta = document.querySelector('meta[name="robots"][content*="noindex"]')
      
      if (!existingNoIndexMeta) {
        const metaRobots = document.createElement('meta')
        metaRobots.name = 'robots'
        metaRobots.content = 'noindex, follow'
        document.head.appendChild(metaRobots)
      }
      
      // 跟踪 UTM 来源
      const utmSource = searchParams.get('utm_source')
      const utmMedium = searchParams.get('utm_medium')
      if (utmSource || utmMedium) {
        trackEvent('utm_visit', 'traffic', `${utmSource || 'unknown'}_${utmMedium || 'unknown'}`)
      }
    }
  }, [searchParams])

  return null // 这个组件不渲染任何内容
}

// 辅助函数：检查当前页面是否应该被索引
export function shouldIndexPage(): boolean {
  if (typeof window === 'undefined') return true
  
  const urlParams = new URLSearchParams(window.location.search)
  
  // 如果有这些参数，不应该被索引
  const noIndexParams = ['ref', 'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content']
  
  return !noIndexParams.some(param => urlParams.has(param))
}

// 辅助函数：获取规范 URL（去除所有跟踪参数）
export function getCanonicalUrl(): string {
  if (typeof window === 'undefined') return 'https://proratedrent.club/'
  
  const url = new URL(window.location.href)
  
  // 移除所有跟踪参数
  const trackingParams = ['ref', 'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content']
  trackingParams.forEach(param => url.searchParams.delete(param))
  
  return url.toString()
}
