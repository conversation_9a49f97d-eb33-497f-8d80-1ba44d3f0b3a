export async function GET() {
  const baseUrl = 'https://proratedrent.club'
  const currentDate = new Date().toISOString().split('T')[0]

  // 构建 XML 内容，确保 XML 声明在最前面
  const xmlDeclaration = '<?xml version="1.0" encoding="UTF-8"?>'
  const urlsetOpen = '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">'
  const urlContent = `  <url>
    <loc>${baseUrl}/</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>`
  const urlsetClose = '</urlset>'

  const sitemap = [xmlDeclaration, urlsetOpen, urlContent, urlsetClose].join('\n')

  return new Response(sitemap, {
    status: 200,
    headers: {
      'Content-Type': 'text/xml; charset=utf-8',
      'Cache-Control': 'public, max-age=86400',
      'X-Content-Type-Options': 'nosniff',
    },
  })
}
