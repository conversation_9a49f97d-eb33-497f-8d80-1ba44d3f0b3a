export const seoConfig = {
  // 网站基本信息
  siteName: 'Prorated Rent Calculator',
  siteUrl: 'https://proratedrent.club',
  description: 'Calculate prorated rent for move-in and move-out dates. Fair rent calculation for partial months.',
  
  // 关键词
  keywords: [
    'prorated rent calculator',
    'rent calculator',
    'partial month rent',
    'move in rent calculation',
    'move out rent calculation',
    'rental calculator',
    'apartment rent calculator',
    'fair rent calculation'
  ],
  
  // Open Graph 配置
  openGraph: {
    type: 'website',
    locale: 'en_US',
    siteName: 'Prorated Rent Calculator',
    title: 'Prorated Rent Calculator - Fair Rent Calculation',
    description: 'Calculate prorated rent for move-in and move-out dates. Fair rent calculation for partial months.',
    images: [
      {
        url: '/og-image.png', // 需要创建这个图片
        width: 1200,
        height: 630,
        alt: 'Prorated Rent Calculator'
      }
    ]
  },
  
  // Twitter Card 配置
  twitter: {
    card: 'summary_large_image',
    title: 'Prorated Rent Calculator',
    description: 'Calculate prorated rent for move-in and move-out dates. Fair rent calculation for partial months.',
    images: ['/og-image.png']
  },
  
  // 结构化数据
  structuredData: {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: 'Prorated Rent Calculator',
    description: 'Calculate prorated rent for move-in and move-out dates. Fair rent calculation for partial months.',
    url: 'https://proratedrent.club',
    applicationCategory: 'FinanceApplication',
    operatingSystem: 'Web Browser',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD'
    },
    author: {
      '@type': 'Organization',
      name: 'Prorated Rent Calculator'
    }
  }
}

// 生成完整的 metadata 对象
export function generateMetadata(customTitle?: string, customDescription?: string) {
  const title = customTitle || seoConfig.siteName
  const description = customDescription || seoConfig.description
  
  return {
    title,
    description,
    keywords: seoConfig.keywords.join(', '),
    authors: [{ name: 'Prorated Rent Calculator' }],
    creator: 'Prorated Rent Calculator',
    publisher: 'Prorated Rent Calculator',
    
    // Open Graph
    openGraph: {
      ...seoConfig.openGraph,
      title,
      description,
      url: seoConfig.siteUrl,
    },
    
    // Twitter
    twitter: {
      ...seoConfig.twitter,
      title,
      description,
    },
    
    // 规范链接
    alternates: {
      canonical: seoConfig.siteUrl,
    },
    
    // 其他 meta 标签
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    
    // 验证标签（如果需要）
    verification: {
      // google: 'your-google-verification-code',
      // bing: 'your-bing-verification-code',
    },
  }
}
