"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const months = [
  { name: "January", days: 31 },
  { name: "February", days: 28 },
  { name: "March", days: 31 },
  { name: "April", days: 30 },
  { name: "May", days: 31 },
  { name: "June", days: 30 },
  { name: "July", days: 31 },
  { name: "August", days: 31 },
  { name: "September", days: 30 },
  { name: "October", days: 31 },
  { name: "November", days: 30 },
  { name: "December", days: 31 },
]

export default function ProratedRentCalculator() {
  // Move-in calculator state
  const [moveInRent, setMoveInRent] = useState<number>(0)
  const [moveInMonth, setMoveInMonth] = useState<string>("January")
  const [moveInDay, setMoveInDay] = useState<number>(1)
  const [securityDeposit, setSecurityDeposit] = useState<number>(0)
  const [otherMoveInCosts, setOtherMoveInCosts] = useState<number>(0)

  // Move-out calculator state
  const [moveOutRent, setMoveOutRent] = useState<number>(0)
  const [moveOutMonth, setMoveOutMonth] = useState<string>("January")
  const [moveOutDay, setMoveOutDay] = useState<number>(1)

  // Calculated values
  const [moveInProratedRent, setMoveInProratedRent] = useState<number>(0)
  const [moveOutProratedRent, setMoveOutProratedRent] = useState<number>(0)
  const [totalMoveInCost, setTotalMoveInCost] = useState<number>(0)

  // Calculate prorated rent for move-in
  useEffect(() => {
    const selectedMonth = months.find((m) => m.name === moveInMonth)
    if (selectedMonth && moveInRent > 0) {
      const daysInMonth = selectedMonth.days
      const daysOccupied = daysInMonth - moveInDay + 1
      const dailyRent = moveInRent / daysInMonth
      const prorated = dailyRent * daysOccupied
      setMoveInProratedRent(prorated)
    } else {
      setMoveInProratedRent(0)
    }
  }, [moveInRent, moveInMonth, moveInDay])

  // Calculate prorated rent for move-out
  useEffect(() => {
    const selectedMonth = months.find((m) => m.name === moveOutMonth)
    if (selectedMonth && moveOutRent > 0) {
      const daysInMonth = selectedMonth.days
      const daysOccupied = moveOutDay
      const dailyRent = moveOutRent / daysInMonth
      const prorated = dailyRent * daysOccupied
      setMoveOutProratedRent(prorated)
    } else {
      setMoveOutProratedRent(0)
    }
  }, [moveOutRent, moveOutMonth, moveOutDay])

  // Calculate total move-in cost
  useEffect(() => {
    setTotalMoveInCost(moveInProratedRent + securityDeposit + otherMoveInCosts)
  }, [moveInProratedRent, securityDeposit, otherMoveInCosts])

  const getDaysInMonth = (monthName: string) => {
    const month = months.find((m) => m.name === monthName)
    return month ? month.days : 31
  }

  const generateDayOptions = (monthName: string) => {
    const daysInMonth = getDaysInMonth(monthName)
    return Array.from({ length: daysInMonth }, (_, i) => i + 1)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white py-16 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-blue-900 mb-8 tracking-tight">PRORATED RENT CALCULATOR</h1>
          <h2 className="text-3xl md:text-4xl font-bold text-blue-800 mb-8">
            Prorating rent is a breeze with our calculator.
          </h2>
          <div className="max-w-3xl mx-auto space-y-4 text-lg text-gray-700">
            <p>
              Whether you are moving in or out a tenant, all you need is your{" "}
              <span className="font-semibold">rent amount</span>,{" "}
              <span className="font-semibold">the month of move-in or move-out</span>, and{" "}
              <span className="font-semibold">additional move-in costs</span>, if applicable.
            </p>
            <p>Input the correct numbers in the calculator below to discover your prorated rent amount.</p>
          </div>
        </div>
      </div>

      {/* Calculator Section */}
      <div className="py-16 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="grid md:grid-cols-2 gap-8">
            {/* Move-in Calculator */}
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-gray-800">Move-in calculator</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="moveInRent" className="text-sm font-medium text-gray-700">
                    Monthly rent
                  </Label>
                  <div className="relative mt-1">
                    <Input
                      id="moveInRent"
                      type="number"
                      value={moveInRent || ""}
                      onChange={(e) => setMoveInRent(Number(e.target.value))}
                      className="pr-8"
                      placeholder="0"
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="moveInMonth" className="text-sm font-medium text-gray-700">
                      Move-in month
                    </Label>
                    <Select value={moveInMonth} onValueChange={setMoveInMonth}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {months.map((month) => (
                          <SelectItem key={month.name} value={month.name}>
                            {month.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="moveInDay" className="text-sm font-medium text-gray-700">
                      Move-in day of month
                    </Label>
                    <Select value={moveInDay.toString()} onValueChange={(value) => setMoveInDay(Number(value))}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {generateDayOptions(moveInMonth).map((day) => (
                          <SelectItem key={day} value={day.toString()}>
                            {day}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="bg-blue-100 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-blue-800 font-medium">1st month prorated rent</span>
                    <span className="text-blue-900 font-bold text-lg">${moveInProratedRent.toFixed(2)}</span>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-800 mb-4">
                    Additional move-in costs <span className="text-sm text-gray-500 font-normal">(optional)</span>
                  </h3>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="securityDeposit" className="text-sm font-medium text-gray-700">
                        Security deposit
                      </Label>
                      <div className="relative mt-1">
                        <Input
                          id="securityDeposit"
                          type="number"
                          value={securityDeposit || ""}
                          onChange={(e) => setSecurityDeposit(Number(e.target.value))}
                          className="pr-8"
                          placeholder="0"
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="otherCosts" className="text-sm font-medium text-gray-700">
                        Other move-in costs
                      </Label>
                      <div className="relative mt-1">
                        <Input
                          id="otherCosts"
                          type="number"
                          value={otherMoveInCosts || ""}
                          onChange={(e) => setOtherMoveInCosts(Number(e.target.value))}
                          className="pr-8"
                          placeholder="0"
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-100 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-blue-800 font-medium">Total move-in cost</span>
                    <span className="text-blue-900 font-bold text-lg">${totalMoveInCost.toFixed(2)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Move-out Calculator */}
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-gray-800">Move-out calculator</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="moveOutRent" className="text-sm font-medium text-gray-700">
                    Monthly rent
                  </Label>
                  <div className="relative mt-1">
                    <Input
                      id="moveOutRent"
                      type="number"
                      value={moveOutRent || ""}
                      onChange={(e) => setMoveOutRent(Number(e.target.value))}
                      className="pr-8"
                      placeholder="0"
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="moveOutMonth" className="text-sm font-medium text-gray-700">
                      Move-out month
                    </Label>
                    <Select value={moveOutMonth} onValueChange={setMoveOutMonth}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {months.map((month) => (
                          <SelectItem key={month.name} value={month.name}>
                            {month.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="moveOutDay" className="text-sm font-medium text-gray-700">
                      Move-out day of month
                    </Label>
                    <Select value={moveOutDay.toString()} onValueChange={(value) => setMoveOutDay(Number(value))}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {generateDayOptions(moveOutMonth).map((day) => (
                          <SelectItem key={day} value={day.toString()}>
                            {day}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="bg-blue-100 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-blue-800 font-medium">Move-out prorated rent</span>
                    <span className="text-blue-900 font-bold text-lg">${moveOutProratedRent.toFixed(2)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* FAQ and Information Section */}
      <div className="bg-white py-16 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Table of contents</h2>
            <div className="space-y-2 text-blue-600">
              <div>
                •{" "}
                <a href="#what-is" className="hover:underline">
                  What is prorated rent?
                </a>
              </div>
              <div>
                •{" "}
                <a href="#why-prorate" className="hover:underline">
                  Why prorate rent?
                </a>
              </div>
              <div>
                •{" "}
                <a href="#how-calculated" className="hover:underline">
                  How is prorated rent calculated?
                </a>
              </div>
              <div>
                •{" "}
                <a href="#example" className="hover:underline">
                  Example of prorated rent calculation
                </a>
              </div>
              <div>
                •{" "}
                <a href="#months-length" className="hover:underline">
                  The months' length
                </a>
              </div>
              <div>
                •{" "}
                <a href="#when-request" className="hover:underline">
                  When can you request prorated rent?
                </a>
              </div>
              <div>
                •{" "}
                <a href="#faqs" className="hover:underline">
                  FAQs
                </a>
              </div>
            </div>
          </div>

          <div className="prose prose-lg max-w-none">
            <p className="text-lg text-gray-700 mb-8">
              If you don't occupy a rented apartment for the entire month, this prorated rent calculator will surely
              come in handy. It allows you to easily determine the rent you need to pay if you're moving in at any date
              other than the first of the month. Read on to learn:
            </p>

            <ul className="list-disc pl-6 mb-8 text-gray-700">
              <li>
                <strong>What prorated rent is;</strong>
              </li>
              <li>
                <strong>Why prorated rent is a good thing;</strong>
              </li>
              <li>
                <strong>How to calculate prorated rent;</strong>
              </li>
              <li>
                <strong>How to use our prorated rent calculator;</strong> and
              </li>
              <li>
                <strong>When you can request prorated rent.</strong>
              </li>
            </ul>

            <p className="text-gray-700 mb-12">
              Additionally, we have provided an example of the prorates rent calculation to make things clearer for you.
            </p>

            <section id="what-is" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">What is prorated rent?</h2>
              <p className="text-gray-700 mb-4">
                Imagine that you are moving into a new apartment. You have already talked to the landlord and agreed to
                move in on the 20th of September. Typically, rent is charged every month; it seems unfair, though, to
                demand from you a full month's rent for September. A fair solution would be to charge you prorated rent
                - a certain percentage of the total rent proportional to the number of days you'll be staying in the
                apartment.
              </p>
              <p className="text-gray-700 mb-4">
                When prorated rent is applied, and you move into an apartment on the 20th of September (which has 30
                days), your payment for this month would be one-third (10 / 30), i.e. about 33% of the full month's
                rent. Similarly, the prorated rent could be used to calculate the amount of the rent due if your
                landlord asks you to move out before the end of the month or if you want to stay an extra couple of days
                at the beginning of next month.
              </p>
              <p className="text-gray-700">
                So what is prorated rent exactly? The formal prorated rent definition is the portion of the rental rate
                that corresponds to whatever part of the month the tenant lives in (or has access to) the property. Or
                in other words, the prorated rent is the amount of money a landlord charges a tenant for occupying the
                rented property for part of the base rental period (usually a month).
              </p>
            </section>

            <section id="why-prorate" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Why prorate rent?</h2>
              <p className="text-gray-700 mb-4">
                The easiest and best explanation is that it is the only way to be fair!
              </p>
              <p className="text-gray-700 mb-4">
                More specifically, this fairness refers to the way the landlord treats his tenant from the first day. It
                is because rent proration is a mechanism that is used to calculate the rent amount due for a month
                during which the tenant did not fully occupy the property.
              </p>
              <p className="text-gray-700">
                So to answer the question of why prorate rent, the tenant has a favorable view of his landlord. It is
                always good to develop a good relationship with the people with whom you do business. After all, they
                can also influence those with whom you do business in the future. Thanks to prorating, the landlord
                receives the correct amount of rent due, and the tenant only pays for the actual period they stayed in
                the property. Everybody wins. It doesn't hurt that the tenant now has a favorable view of his landlord.
              </p>
            </section>

            <section id="how-calculated" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">How is prorated rent calculated?</h2>
              <p className="text-gray-700 mb-4">
                So are you now wondering how to prorate rent? To calculate the amount of prorated rent, you first need
                to determine a daily rent amount. To do so, you have to divide the total rent amount by the number of
                days in a month. Then you should multiply the obtained daily rent amount by the number of days you will
                be occupying the property in that month. The result of this operation is the prorated rent amount for
                the partial month of renting.
              </p>
              <p className="text-gray-700 mb-4">
                To compute the prorated rent amount, it is easier to use our prorated rent calculator. However, if you
                want to know how the calculations are done, you should take a look at the following steps. A practical
                example of prorated rent calculations is provided in the next section. Don't worry - it's easier than
                you think!
              </p>
              <ol className="list-decimal pl-6 text-gray-700 space-y-2">
                <li>Pick your move-in or move-out date.</li>
                <li>
                  Check how many days there are in the month of your move-in (if you are not sure, you can find the
                  necessary information in the section called the months' length). Note this number down.
                </li>
                <li>Count the number of days in the month that you will occupy the apartment.</li>
                <li>
                  Check the monthly rent amount in your rental agreement or ask your potential landlord about its value.
                </li>
                <li>Divide the monthly rent by the total number of days in the month.</li>
                <li>Finally, multiply this value by the number of days you plan to occupy the apartment.</li>
              </ol>
              <p className="text-gray-700 mt-4">
                That's it. The result of the last operation is the exact amount of your prorated rent.
              </p>
            </section>

            <section id="example" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Example of prorated rent calculation</h2>
              <p className="text-gray-700 mb-4">
                In the following example, let's make two assumptions: you move into a rented flat on the 15th of
                September, and the monthly rent is $650.
              </p>
              <p className="text-gray-700 mb-4">
                Below you can see a step-by-step guide on how to calculate the prorated rent:
              </p>
              <ul className="list-disc pl-6 text-gray-700 space-y-2 mb-4">
                <li>
                  <strong>Move-in date:</strong> the 15th of September
                </li>
                <li>
                  <strong>Days in September:</strong> 30
                </li>
                <li>
                  <strong>Number of days of property occupancy:</strong> 16
                </li>
                <li>
                  <strong>Monthly amount of rent:</strong> $650
                </li>
                <li>
                  <strong>Daily rent amount</strong> (monthly rent amount divided by the number of days in a month):
                  $650 / 30 = $21.67
                </li>
                <li>
                  <strong>Prorated rent:</strong> $21.6667 * 16 = $346.67
                </li>
              </ul>
              <p className="text-gray-700 mb-4">
                Remember to be very careful with calculating the number of days you will occupy the apartment in a
                month. In our example, you count the 15th of September as the first day, the 16th as the second day, and
                so on. In total, you will occupy this apartment for 16 days.
              </p>
              <p className="text-gray-700">
                Finally, your prorated rent in September is $346.67. If you fill the appropriate fields of our prorated
                rent calculator, you will receive the same value.
              </p>
            </section>

            <section id="months-length" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">The months' length</h2>
              <p className="text-gray-700 mb-6">
                As you have seen, calculating prorated rent is generally a piece of cake. All you need to know is the
                amount of monthly rent and the number of days in the month. The former will be in the rental agreement.
                The latter is a bit more complicated as not every month has the same number of days. Thanks, Caesar.
                However, this number is the same every year (except February, which changes every leap year), so if you
                are unsure of the correct values, you can check it in the table below.
              </p>

              <div className="overflow-x-auto mb-6">
                <table className="min-w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="border border-gray-300 px-4 py-2 text-left">Month</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">Number of days</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">Month</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">Number of days</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2">January</td>
                      <td className="border border-gray-300 px-4 py-2">31</td>
                      <td className="border border-gray-300 px-4 py-2">July</td>
                      <td className="border border-gray-300 px-4 py-2">31</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2">February</td>
                      <td className="border border-gray-300 px-4 py-2">28 (29 in leap years)</td>
                      <td className="border border-gray-300 px-4 py-2">August</td>
                      <td className="border border-gray-300 px-4 py-2">31</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2">March</td>
                      <td className="border border-gray-300 px-4 py-2">31</td>
                      <td className="border border-gray-300 px-4 py-2">September</td>
                      <td className="border border-gray-300 px-4 py-2">30</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2">April</td>
                      <td className="border border-gray-300 px-4 py-2">30</td>
                      <td className="border border-gray-300 px-4 py-2">October</td>
                      <td className="border border-gray-300 px-4 py-2">31</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2">May</td>
                      <td className="border border-gray-300 px-4 py-2">31</td>
                      <td className="border border-gray-300 px-4 py-2">November</td>
                      <td className="border border-gray-300 px-4 py-2">30</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2">June</td>
                      <td className="border border-gray-300 px-4 py-2">30</td>
                      <td className="border border-gray-300 px-4 py-2">December</td>
                      <td className="border border-gray-300 px-4 py-2">31</td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <p className="text-gray-700 mb-4">
                🙋 Did you know that the mean month length of the Gregorian calendar is 30.436875 days? If you multiply
                this number by 12, it gives 365.2425. That is why we have leap years (or intercalary years) when
                February has one additional day.
              </p>

              <div className="bg-blue-50 p-4 rounded-lg mb-4">
                <p className="text-gray-700 italic">
                  "Thirty days has September
                  <br />
                  April, June, and November.
                  <br />
                  All the rest have thirty-one
                  <br />
                  Excepting February alone,
                  <br />
                  Which only has but 28 days clear
                  <br />
                  And 29 in each leap year."
                </p>
              </div>
            </section>

            <section id="when-request" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">When can you request prorated rent?</h2>
              <p className="text-gray-700 mb-4">
                There is no federal or state law regarding prorated rent; only in some places is prorated rent required
                by law. Usually, most of the landlords will agree to prorate rent if you move in during the month.
                However, don't be surprised if they deny prorating rent when you suddenly decide to move out. It is
                advisable always to check the possibility of prorating with your landlord. Also, try to include it in
                the rental contract.
              </p>
              <p className="text-gray-700">
                Since it is in the landlord's best interest to have his apartments rented at all times, you may request
                prorated rent be a part of your agreement should you leave in the middle of a month.
              </p>
            </section>

            <section id="faqs" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">FAQs</h2>

              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">What is prorated rent?</h3>
                  <p className="text-gray-700">
                    Prorated rent is a way to consider the actual moving-in date when starting a rental contract for any
                    house or apartment. If you are not starting your stay at the beginning of the month, paying the
                    entire month's rent would be unfair: the prorated rent allows you to pay only the exact amount due.
                    Prorated rent is calculated proportionally, depending on the day of the month you will move in.
                    Always ask for this type of payment if you are eligible!
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">How do I calculate the prorated rent?</h3>
                  <p className="text-gray-700 mb-2">
                    To calculate the prorated rent, you can set up a proportion. Follow these simple steps:
                  </p>
                  <ol className="list-decimal pl-6 text-gray-700 space-y-1">
                    <li>
                      Set up the following proportion: monthly rent : days in month = prorated rent : effective stay,
                      where:
                      <ul className="list-disc pl-6 mt-1">
                        <li>
                          <strong>days in month</strong> — The exact number of days of the month you're moving in or
                          out; and
                        </li>
                        <li>
                          <strong>effective stay</strong> — The effective number of days of your stay in the first or
                          last month.
                        </li>
                      </ul>
                    </li>
                    <li>Calculate the daily rent for the given month: daily rent = monthly rent/days in month.</li>
                    <li>
                      Multiply the daily rent by the effective number of days to find the prorated rent: prorated rent =
                      daily rent × effective stay.
                    </li>
                  </ol>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    What is the prorated rent if I move on the 6th of June?
                  </h3>
                  <p className="text-gray-700 mb-2">
                    If you moved on the 6th of June, the prorated rent would be 83.3% of the total monthly rent. To find
                    this result, follow these steps:
                  </p>
                  <ol className="list-decimal pl-6 text-gray-700 space-y-1">
                    <li>Calculate the daily rent for June: daily rent = monthly rent/30, since June has 30 days.</li>
                    <li>
                      Calculate the effective stay: effective stay = 30 - 6 + 1= 25. Why +1? Because your first day in
                      counts!
                    </li>
                    <li>
                      Multiply the daily rent by the effective stay to find the prorated rent: prorated rent = monthly
                      rent × 25/30 = monthly rent × 0.833.
                    </li>
                  </ol>
                  <p className="text-gray-700 mt-2">Thus the prorated rent is 83.3% of the monthly one.</p>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Can my landlord refuse prorated rent?</h3>
                  <p className="text-gray-700">
                    Prorated rent is not usually regulated by law: it will depend on your landlord if you can pay for
                    the total number of days you will spend in your rented apartment. Usually, it's easier to ask for
                    prorated rent in the first month than in the last month, and it's always better to ask for a written
                    agreement in advance if you are interested in this type of payment.
                  </p>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  )
}
