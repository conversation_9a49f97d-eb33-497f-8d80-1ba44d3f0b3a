import type { Metadata } from 'next'
import { GeistSans } from 'geist/font/sans'
import { GeistMono } from 'geist/font/mono'
import { Analytics } from '@/components/analytics'
import { SEOHandler } from '@/components/seo-handler'
import { generateMetadata, seoConfig } from '@/lib/seo-config'
import { Suspense } from 'react'
import './globals.css'

export const metadata: Metadata = generateMetadata()

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        <style>{`
html {
  font-family: ${GeistSans.style.fontFamily};
  --font-sans: ${GeistSans.variable};
  --font-mono: ${GeistMono.variable};
}
        `}</style>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(seoConfig.structuredData),
          }}
        />
      </head>
      <body>
        {children}
        <Analytics />
        <Suspense fallback={null}>
          <SEOHandler />
        </Suspense>
      </body>
    </html>
  )
}
