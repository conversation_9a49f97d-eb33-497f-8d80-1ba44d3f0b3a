// 验证 sitemap 的脚本
const https = require('https');

function validateSitemap(url) {
  console.log(`正在验证 sitemap: ${url}`);
  
  https.get(url, (res) => {
    let data = '';
    
    console.log(`状态码: ${res.statusCode}`);
    console.log(`Content-Type: ${res.headers['content-type']}`);
    
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('\n=== Sitemap 内容 ===');
      console.log(data);
      
      // 基本验证
      const checks = [
        {
          name: 'XML 声明',
          test: data.includes('<?xml version="1.0"'),
          required: true
        },
        {
          name: 'UTF-8 编码',
          test: data.includes('encoding="UTF-8"'),
          required: true
        },
        {
          name: 'urlset 命名空间',
          test: data.includes('xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"'),
          required: true
        },
        {
          name: 'URL 元素',
          test: data.includes('<url>') && data.includes('</url>'),
          required: true
        },
        {
          name: 'loc 元素',
          test: data.includes('<loc>') && data.includes('</loc>'),
          required: true
        },
        {
          name: 'lastmod 元素',
          test: data.includes('<lastmod>') && data.includes('</lastmod>'),
          required: false
        }
      ];
      
      console.log('\n=== 验证结果 ===');
      let allPassed = true;
      
      checks.forEach(check => {
        const status = check.test ? '✅ 通过' : '❌ 失败';
        const required = check.required ? '(必需)' : '(可选)';
        console.log(`${status} ${check.name} ${required}`);
        
        if (check.required && !check.test) {
          allPassed = false;
        }
      });
      
      console.log(`\n总体结果: ${allPassed ? '✅ 通过' : '❌ 有问题'}`);
      
      if (!allPassed) {
        console.log('\n建议修复:');
        console.log('1. 确保 XML 声明在第一行');
        console.log('2. 检查命名空间是否正确');
        console.log('3. 验证所有必需元素是否存在');
      }
    });
    
  }).on('error', (err) => {
    console.error(`请求错误: ${err.message}`);
  });
}

// 验证主 sitemap
validateSitemap('https://proratedrent.club/sitemap.xml');

// 如果需要，也可以验证本地开发环境
// validateSitemap('http://localhost:3000/sitemap.xml');
