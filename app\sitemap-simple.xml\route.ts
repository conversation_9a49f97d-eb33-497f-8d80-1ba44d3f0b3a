import { NextRequest } from 'next/server'

export async function GET(request: NextRequest) {
  // 最简单的 sitemap 实现
  const xml = '<?xml version="1.0" encoding="UTF-8"?>\n' +
    '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n' +
    '  <url>\n' +
    '    <loc>https://proratedrent.club/</loc>\n' +
    '    <lastmod>2025-08-08</lastmod>\n' +
    '    <changefreq>weekly</changefreq>\n' +
    '    <priority>1.0</priority>\n' +
    '  </url>\n' +
    '</urlset>'

  return new Response(xml, {
    headers: {
      'Content-Type': 'application/xml',
    },
  })
}
